/* Modern Dialog Styles */
.modern-dialog {
  min-width: 320px;
  max-width: 100%;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Header */
.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border-bottom: 1px solid var(--border-color);
}

.modern-card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-header-icon {
  font-size: 1.1rem;
}

.modern-btn-icon {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-speed) ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modern-btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Body */
.modern-card-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-label-icon {
  color: var(--primary);
  font-size: 0.85rem;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-speed) ease;
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Form Row */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-col {
  min-width: 0;
}

/* Input with Prefix */
.input-with-prefix {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  z-index: 1;
  pointer-events: none;
}

.modern-form-control.with-prefix {
  padding-left: 2rem;
}

/* Form Hint */
.form-hint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

/* Form Error */
.form-error {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--danger);
  margin-top: 0.25rem;
}

/* Footer */
.modern-card-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* Modern Buttons */
.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  text-decoration: none;
  min-width: 100px;
  justify-content: center;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.modern-btn-primary {
  background-color: var(--primary);
  color: white;
}

.modern-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn-outline-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.modern-btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--text-secondary);
}

.modern-btn-icon {
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-dialog {
    min-width: 280px;
    margin: 1rem;
  }

  .modern-card-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modern-card-header h2 {
    font-size: 1.1rem;
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .modern-card-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .modern-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modern-dialog {
    min-width: 260px;
  }

  .modern-card-body {
    padding: var(--spacing-sm);
  }

  .form-group {
    margin-bottom: var(--spacing-sm);
  }
}

/* Dark Mode Support */
[data-theme="dark"] .modern-dialog {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .modern-card-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modern-card-footer {
  background-color: var(--bg-tertiary);
  border-top-color: var(--border-color);
}

[data-theme="dark"] .modern-form-control {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.2);
}

[data-theme="dark"] .modern-btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-secondary);
}

[data-theme="dark"] .modern-btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--bg-primary);
  border-color: var(--text-primary);
  color: var(--text-primary);
}